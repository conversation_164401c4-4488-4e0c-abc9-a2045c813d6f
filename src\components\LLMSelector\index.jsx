import { useEffect, useState } from 'react';
import { groupBy } from 'lodash';

import { iconToCompanyName, LLM_MODEL_DEFAULT } from '@/constants';
import loadIcon from '@/helpers/loadIcons';
import useDanteApi from '@/hooks/useDanteApi';
import { getLLMModels } from '@/services/model.service';
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';
import { updateChatbotLLMModel } from '@/services/chatbot.service';
import ChevronDownIcon from '../Global/Icons/ChevronDownIcon';

import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import OpenAiLogoIcon from '../Global/Icons/OpenAiLogoIcon';
import useToast from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { useParams } from 'react-router-dom';
import featureCheck, { checkFeatureAvailability } from '@/helpers/tier/featureCheck';
import SelectedIcon from '../Global/Icons/SelectedIcon';

const LLMSelector = () => {
  const { user } = useUserStore();
  const params = useParams();
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const [selectedLLM, setSelectedLLM] = useState(selectedChatbot?.last_model_used ?? LLM_MODEL_DEFAULT);
  const setSelectedLLModel = useChatbotStore(
    (state) => state.setSelectedLLModel
  );
  const [icons, setIcons] = useState({});

  const [modelsByCompany, setModelsByCompany] = useState({});

  const { data: models } = useDanteApi(getLLMModels, [], {}, {
    tier_type: user.tier_type,
  });

  const { addWarningToast } = useToast();

  const handleChangeModel = async (model) => {
   
    if(model.value === 'cohere' && !featureCheck('cohere_model')) {
      return;
    }

    if(model.value.includes('claude') && !featureCheck('claude_model')) {
      return;
    }

    if (!model.available) {
      addWarningToast({
        message:
          'This model is not available for your plan. Please upgrade your plan to use this model.',
      });
      return;
    }
    setSelectedLLM({ ...model, Icon: icons[model.Logo] });
    setSelectedLLModel(model);

    try{
        await updateChatbotLLMModel({
            kb_id: params?.id,
            llmModel: model.value
        });
    } catch (e) {
        console.error('Failed to update chatbot LLM model', e);
    }
  };

  useEffect(() => {
    const loadModels = async () => {
      const groupedModels = groupBy(models, 'Logo');

      const iconComponents = {};

      for (const company of Object.keys(groupedModels)) {
        try {
          const iconCompany = await loadIcon(company);
          iconComponents[company] = iconCompany;
        } catch (e) {
          console.error(`Failed to load icon ${company}`, e);
        }
      }

      setIcons(iconComponents);
      setModelsByCompany(groupedModels);
    };
    if (models?.length) {
      loadModels();
    }
  }, [models]);

  useEffect(() => {
    setSelectedLLM(selectedChatbot?.last_model_used ?? LLM_MODEL_DEFAULT);
  }, [selectedChatbot?.last_model_used]);

  return (
    <Listbox
      value={selectedLLM}
      onChange={handleChangeModel}
      className="group"
      as="div"
    >
      <ListboxButton className="flex items-center justify-between gap-size1 text-xs ">
        <div className="flex items-center justify-center h-full">
          {selectedLLM.Icon ? (
            <selectedLLM.Icon className="size-4" />
          ) : (
            <OpenAiLogoIcon className="size-4" />
          )}
        </div>
        <div className="flex items-center justify-center">
          {selectedLLM.label}
        </div>
        <ChevronDownIcon
           className="transition-transform group pointer-events-none size-3 fill-black group-data-[open]:rotate-180"
          aria-hidden="true"
        />
      </ListboxButton>
      <ListboxOptions
        anchor={{
          to: 'top start',
          gap: 8,
        }}
        transition
        className="flex flex-col  gap-size2 py-size2 rounded-size2 border border-grey-10 bg-white origin-bottom transition duration-200 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 z-[999] !max-h-[500px] overflow-y-auto scrollbar"
      >
        {Object.keys(modelsByCompany)?.map((company) => {
          const Icon = icons[company];

          return (
            <div key={company} className="flex flex-col gap-size0">
                  <span className="text-xs font-medium text-grey-50 px-size2">
                {iconToCompanyName[company]}
              </span>
              {modelsByCompany[company].map((model) => {
                const isModelDisabled = !model.available ||
                  (model.value === 'cohere' && !checkFeatureAvailability('cohere_model', user.tier_type)) ||
                  (model.value.includes('claude') && !checkFeatureAvailability('claude_model', user.tier_type));

                return (
                  <ListboxOption
                    as="button"
                    key={model.value}
                    value={model}
                    className={'flex items-center gap-size1 text-sm py-size0 px-size2 cursor-pointer data-[disabled]:opacity-50 data-[disabled]:cursor-default transition-colors duration-150 data-[selected]:font-medium hover:bg-[var(--dt-color-element-5)] data-[selected]:bg-[var(--dt-color-brand-10)] data-[selected]:hover:bg-[var(--dt-color-brand-10)]'}
                    style={{
                      color: 'var(--dt-color-element-100)'
                    }}
                    disabled={isModelDisabled}
                  >
                      {Icon && <Icon className="size-4" />}
                      <div className='flex flex-col items-start flex-1'>
                        <span
                          style={{
                            color: selectedLLM.value === model.value
                              ? 'var(--dt-color-brand-100)'
                              : 'var(--dt-color-element-100)'
                          }}
                        >
                          {model.label}
                        </span>
                        <span
                          className='text-xs leading-none'
                          style={{
                            color: selectedLLM.value === model.value
                              ? 'var(--dt-color-brand-60)'
                              : 'var(--dt-color-element-50)'
                          }}
                        >
                          {model.credits} credits per response
                        </span>
                      </div>
                      {selectedLLM.value === model.value && (
                        <div className="flex items-center justify-center size-4 rounded-full bg-brand-100">
                           <SelectedIcon />
                        </div>
                      )}
                  </ListboxOption>
                );
              })}
            </div>
          );
        })}
      </ListboxOptions>
    </Listbox>
  );
};

export default LLMSelector;
